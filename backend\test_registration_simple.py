#!/usr/bin/env python3

import requests
import json
import sys

def test_registration():
    url = "http://localhost:8000/register"
    
    # Use a unique email to avoid conflicts
    import time
    timestamp = int(time.time())
    
    test_data = {
        "email": f"test{timestamp}@example.com",
        "password": "password123",
        "confirmPassword": "password123",
        "company_name": "TEST Pvt Ltd",
        "mobile_number": "9596960000"
    }
    
    try:
        print("Testing registration endpoint...")
        print(f"URL: {url}")
        print(f"Data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"Response Text: {response.text}")
            
        if response.status_code == 200:
            print("✅ Registration successful!")
            return True
        else:
            print(f"❌ Registration failed with status {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the backend running?")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_registration()
    sys.exit(0 if success else 1)
