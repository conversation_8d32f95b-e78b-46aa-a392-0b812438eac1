from fastapi import <PERSON>AP<PERSON>, Depends, HTTPException, status, Request
from fastapi.security import OAuth2Password<PERSON>earer, OAuth2PasswordRequestForm
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from sqlalchemy.orm import Session, joinedload
from datetime import datetime, timedelta, timezone, UTC
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, field_validator, Field
import os
import traceback
from models import User, Customer, Product, Invoice, InvoiceItem, Base
from database import engine, get_db
from auth import get_current_user, create_access_token, authenticate_user, get_password_hash, ACCESS_TOKEN_EXPIRE_MINUTES
from invoice_generator import generate_invoice_pdf
from utils import calculate_taxable_value, calculate_gst, calculate_grand_total, amount_to_words, generate_invoice_number
from reports import generate_sales_report, generate_tax_summary, generate_excel_report, generate_hsn_summary, generate_monthly_hsn_summary, generate_hsn_summary_for_month, export_hsn_summary_excel
from sqlalchemy.orm import Session

# Import new modules
from profile_management import router as profile_router
from analytics import router as analytics_router
from chatbot import router as chatbot_router
from gst_news import router as news_router
from business_tools import router as business_tools_router
from invoice_templates import router as invoice_templates_router
from subscription_routes import router as subscription_router
from admin_api_routes import router as admin_api_router
from notifications import router as notifications_router
from scheduler import start_subscription_scheduler, stop_subscription_scheduler
from notification_scheduler import start_notification_scheduler, stop_notification_scheduler
from contextlib import asynccontextmanager
import asyncio

# Create tables
Base.metadata.create_all(bind=engine)

# Lifespan context manager for startup/shutdown events
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("🚀 Starting automatic subscription expiry checker...")
    scheduler_task = await start_subscription_scheduler()
    print("✅ Subscription scheduler started - will check daily at 2:00 AM")

    print("🚀 Starting notification scheduler...")
    start_notification_scheduler()
    print("✅ Notification scheduler started - will check daily for subscription notifications")

    yield

    # Shutdown
    print("🛑 Stopping subscription scheduler...")
    stop_subscription_scheduler()
    if scheduler_task:
        scheduler_task.cancel()
        try:
            await scheduler_task
        except asyncio.CancelledError:
            pass
    print("✅ Subscription scheduler stopped")

    print("🛑 Stopping notification scheduler...")
    stop_notification_scheduler()
    print("✅ Notification scheduler stopped")

app = FastAPI(lifespan=lifespan)

# Create uploads directory for profile images
UPLOAD_DIR = "uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)

# Mount the uploads directory
app.mount("/uploads", StaticFiles(directory=UPLOAD_DIR), name="uploads")

# CORS configuration
origins = [
    "http://localhost",
    "http://localhost:3000",
    "http://localhost:8081",
    "http://localhost:8080",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:8080",
    "http://127.0.0.1:8081",
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "*"
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"]
)

app.include_router(profile_router, prefix="", tags=["profile"])
app.include_router(analytics_router, prefix="/analytics", tags=["analytics"])
app.include_router(chatbot_router, prefix="/chatbot", tags=["chatbot"])
app.include_router(news_router, prefix="/gst-news", tags=["gst-news"])
app.include_router(business_tools_router, prefix="/business-tools", tags=["business-tools"])
app.include_router(invoice_templates_router, prefix="", tags=["invoice-templates"])
app.include_router(subscription_router, prefix="/api", tags=["subscription"])
app.include_router(admin_api_router, prefix="/api", tags=["admin"])
app.include_router(notifications_router, prefix="/api", tags=["notifications"])

# Root endpoint
@app.get("/")
def read_root():
    """Root endpoint - API health check"""
    return {
        "message": "GST Billing Software API",
        "status": "running",
        "version": "1.0.0",
        "endpoints": {
            "auth": ["/register", "/token", "/users/me"],
            "customers": ["/customers/", "/customers/search/"],
            "products": ["/products/", "/products/search/"],
            "invoices": ["/invoices/", "/invoices/search/"],
            "reports": ["/reports/sales", "/reports/tax-summary", "/reports/hsn-summary"],
            "business_tools": ["/business-tools/calculate-gst", "/business-tools/calculate-tax", "/business-tools/currency-rates", "/business-tools/generate-barcode", "/business-tools/hsn-search", "/business-tools/validate-pan-tan"],
            "analytics": ["/analytics/dashboard-stats", "/analytics/sales-trends"],
            "chatbot": ["/chatbot/chat"],
            "gst_news": ["/gst-news/latest"],
            "profile": ["/profile/upload-logo", "/profile/company-info"]
        }
    }

@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

# Pydantic models
class UserCreate(BaseModel):
    email: str = Field(..., description="Email address (used as username)")
    password: str = Field(..., min_length=6, description="Password must be at least 6 characters")
    confirmPassword: str = Field(..., description="Confirm password must match password")
    company_name: str = Field(..., min_length=1, description="Company name is required")
    mobile_number: str = Field(..., description="Mobile number must be 10 digits")

    # Add validation for email format
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError("Invalid email format")
        return v.lower()

    # Add validation for mobile number
    @field_validator('mobile_number')
    @classmethod
    def validate_mobile_number(cls, v):
        if not v.isdigit() or len(v) != 10:
            raise ValueError("Mobile number must be 10 digits")
        return v

    # Add validation for password confirmation
    @field_validator('confirmPassword')
    @classmethod
    def passwords_match(cls, v, values):
        if 'password' in values.data and v != values.data['password']:
            raise ValueError('Passwords do not match')
        return v

class UserResponse(BaseModel):
    username: str
    email: str
    company_name: Optional[str] = None
    mobile_number: Optional[str] = None
    address: Optional[str] = None
    state: Optional[str] = None
    state_code: Optional[str] = None
    gstin: Optional[str] = None
    bank_name: Optional[str] = None
    account_number: Optional[str] = None
    branch: Optional[str] = None
    ifsc_code: Optional[str] = None
    profile_completed: Optional[bool] = None
    profile_completion_percentage: Optional[int] = None

class UserInfo(BaseModel):
    username: str
    company_name: str
    email: str

class Token(BaseModel):
    access_token: str
    token_type: str
    user_info: Optional[UserInfo] = None


class CustomerCreate(BaseModel):
    name: str
    address: str
    state: str
    state_code: str
    gstin: str
    mobile_number: str

class CustomerResponse(CustomerCreate):
    id: int

    model_config = {"from_attributes": True}

class ProductCreate(BaseModel):
    description: str
    hsn_code: str
    available_quantity: int
    uom: str
    rate: float

    @field_validator('available_quantity')
    @classmethod
    def validate_quantity(cls, v):
        if v < 0:
            raise ValueError("Available quantity cannot be negative")
        return v

class ProductResponse(ProductCreate):
    id: int

    model_config = {"from_attributes": True}


class InvoiceItemCreate(BaseModel):
    sr_no: int
    description: str
    hsn_code: str
    quantity: float
    uom: str
    rate: float
    discount: float = 0.0  # Added discount field with default value of 0
    sgst_rate: float = 0.0  # Added SGST rate field
    cgst_rate: float = 0.0  # Added CGST rate field
    igst_rate: float = 0.0  # Added IGST rate field

class InvoiceCreate(BaseModel):
    customer_id: int
    invoice_date: Optional[datetime] = None
    po_number: Optional[str] = None
    po_date: Optional[datetime] = None
    reverse_charge: str = "No"
    freight_forwarding: float = 0.0
    vehicle_number: Optional[str] = None
    transporter_name: Optional[str] = None
    # Consignee (Ship To) details
    consignee_name: Optional[str] = None
    consignee_address: Optional[str] = None
    consignee_state: Optional[str] = None
    consignee_state_code: Optional[str] = None
    consignee_gstin: Optional[str] = None
    items: List[InvoiceItemCreate]

class ReportRequest(BaseModel):
    start_date: datetime
    end_date: datetime

    @field_validator('start_date', 'end_date', mode='before')  # Using mode='before' instead of pre=True
    @classmethod
    def parse_dates(cls, value):
        if isinstance(value, str):
            try:
                # Handle ISO format strings
                return datetime.fromisoformat(value.replace('Z', '+00:00'))
            except ValueError as e:
                print(f"Date parsing error: {e}")
                raise ValueError(f"Invalid date format: {value}")
        return value

# Auth endpoints
@app.post("/register", response_model=UserResponse)
def register(user: UserCreate, db: Session = Depends(get_db)):
    try:
        # Check for duplicate email (email is used as username)
        db_email = db.query(User).filter(User.email == user.email).first()
        if db_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        hashed_password = get_password_hash(user.password)
        db_user = User(
            username=user.email,  # Use email as username
            email=user.email,
            hashed_password=hashed_password,
            company_name=user.company_name,
            mobile_number=user.mobile_number,
            # Other fields will be filled during profile completion
            address=None,
            state=None,
            state_code=None,
            gstin=None,
            bank_name=None,
            account_number=None,
            branch=None,
            ifsc_code=None,
            profile_completed=False,
            profile_completion_percentage=0
        )

        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        # Calculate initial profile completion (simplified)
        try:
            completion_percentage = db_user.calculate_profile_completion()
            db_user.profile_completion_percentage = completion_percentage
            db_user.profile_completed = db_user.is_profile_complete_for_invoice()
            db.commit()
            db.refresh(db_user)
        except Exception as calc_error:
            print(f"Profile completion calculation error: {calc_error}")
            # Set default values if calculation fails
            db_user.profile_completion_percentage = 30  # Basic info provided
            db_user.profile_completed = False
            db.commit()
            db.refresh(db_user)

        # Convert SQLAlchemy model to dict and filter only UserResponse fields
        user_dict = {
            "username": db_user.username,
            "email": db_user.email,
            "company_name": db_user.company_name,
            "mobile_number": db_user.mobile_number,
            "address": db_user.address,
            "state": db_user.state,
            "state_code": db_user.state_code,
            "gstin": db_user.gstin,
            "bank_name": db_user.bank_name,
            "account_number": db_user.account_number,
            "branch": db_user.branch,
            "ifsc_code": db_user.ifsc_code,
            "profile_completed": db_user.profile_completed,
            "profile_completion_percentage": db_user.profile_completion_percentage
        }

        return user_dict

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        print(f"Registration error: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )


@app.post("/token", response_model=Token)
def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = authenticate_user(db, form_data.username, form_data.password)
    print(f"User authenticated: {user.username if user else 'None'}")

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Print user details for debugging
    print(f"User details - Username: {user.username}, Company Name: {user.company_name}")

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    # Include company_name and other user details in the token payload
    token_data = {
        "sub": user.username,
        "company_name": user.company_name,
        "email": user.email,
        "user_id": user.id
    }

    print(f"Token data being encoded: {token_data}")

    access_token = create_access_token(
        data=token_data, expires_delta=access_token_expires
    )

    # Return token with additional user info for immediate use
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_info": {
            "username": user.username,
            "company_name": user.company_name,
            "email": user.email
        }
    }

@app.get("/users/me", response_model=UserResponse)
def read_users_me(current_user: User = Depends(get_current_user)):
    # Calculate current profile completion
    completion_percentage = current_user.calculate_profile_completion()
    profile_completed = current_user.is_profile_complete_for_invoice()

    # Convert SQLAlchemy model to dict with only the fields in UserResponse
    return {
        "username": current_user.username,
        "email": current_user.email,
        "company_name": current_user.company_name,
        "mobile_number": current_user.mobile_number,
        "address": current_user.address,
        "state": current_user.state,
        "state_code": current_user.state_code,
        "gstin": current_user.gstin,
        "bank_name": current_user.bank_name,
        "account_number": current_user.account_number,
        "branch": current_user.branch,
        "ifsc_code": current_user.ifsc_code,
        "profile_completed": profile_completed,
        "profile_completion_percentage": completion_percentage
    }

@app.get("/profile/completion")
def get_profile_completion(current_user: User = Depends(get_current_user)):
    """Get profile completion status"""
    completion_percentage = current_user.calculate_profile_completion()
    profile_completed = current_user.is_profile_complete_for_invoice()

    return {
        "profile_completion_percentage": completion_percentage,
        "profile_completed": profile_completed,
        "can_create_invoice": current_user.can_create_invoice(),
        "required_for_invoice": {
            "personal_info": {
                "company_name": bool(current_user.company_name and current_user.company_name.strip()),
                "mobile_number": bool(current_user.mobile_number and current_user.mobile_number.strip()),
                "email": bool(current_user.email and current_user.email.strip())
            },
            "company_details": {
                "address": bool(current_user.address and current_user.address.strip()),
                "state": bool(current_user.state and current_user.state.strip()),
                "state_code": bool(current_user.state_code and current_user.state_code.strip()),
                "gstin": bool(current_user.gstin and current_user.gstin.strip())
            }
        }
    }

# Customer endpoints
@app.post("/customers/", response_model=CustomerResponse)
def create_customer(customer: CustomerCreate, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    db_customer = Customer(
        name=customer.name,
        address=customer.address,
        state=customer.state,
        state_code=customer.state_code,
        gstin=customer.gstin,
        mobile_number=customer.mobile_number,
        user_id=current_user.id
    )
    db.add(db_customer)
    db.commit()
    db.refresh(db_customer)
    return db_customer

@app.get("/customers/", response_model=List[CustomerResponse])
def read_customers(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    customers = db.query(Customer).filter(Customer.user_id == current_user.id).offset(skip).limit(limit).all()

    # Convert SQLAlchemy models to dictionaries
    customer_dicts = []
    for customer in customers:
        customer_dicts.append({
            "id": customer.id,
            "name": customer.name,
            "address": customer.address,
            "state": customer.state,
            "state_code": customer.state_code,
            "gstin": customer.gstin,
            "mobile_number": customer.mobile_number
        })

    return customer_dicts


@app.get("/customers/search/")
def search_customers(query: str, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    customers = db.query(Customer).filter(
        Customer.user_id == current_user.id,
        Customer.name.ilike(f"%{query}%")
    ).all()
    return customers
# Update a customer
@app.put("/customers/{customer_id}", response_model=CustomerResponse)
def update_customer(
    customer_id: int,
    customer: CustomerCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    db_customer = db.query(Customer).filter(
        Customer.id == customer_id,
        Customer.user_id == current_user.id
    ).first()

    if not db_customer:
        raise HTTPException(status_code=404, detail="Customer not found")

    # Update customer fields
    db_customer.name = customer.name
    db_customer.address = customer.address
    db_customer.state = customer.state
    db_customer.state_code = customer.state_code
    db_customer.gstin = customer.gstin
    db_customer.mobile_number = customer.mobile_number

    db.commit()
    db.refresh(db_customer)
    return db_customer

# Delete a customer
@app.delete("/customers/{customer_id}", status_code=204)
def delete_customer(
    customer_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    db_customer = db.query(Customer).filter(
        Customer.id == customer_id,
        Customer.user_id == current_user.id
    ).first()

    if not db_customer:
        raise HTTPException(status_code=404, detail="Customer not found")

    db.delete(db_customer)
    db.commit()
    return None

# Product endpoints
@app.post("/products/", response_model=ProductResponse)
def create_product(product: ProductCreate, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    try:
        # Ensure available_quantity is an integer
        available_quantity = int(product.available_quantity)

        db_product = Product(
            description=product.description,
            hsn_code=product.hsn_code,
            available_quantity=available_quantity,
            uom=product.uom,
            rate=product.rate,
            user_id=current_user.id
        )
        db.add(db_product)
        db.commit()
        db.refresh(db_product)
        return db_product
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Available quantity must be a valid integer"
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create product: {str(e)}"
        )

@app.get("/products/", response_model=List[ProductResponse])
def read_products(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    try:
        products = db.query(Product).filter(Product.user_id == current_user.id).offset(skip).limit(limit).all()

        # Convert SQLAlchemy models to dictionaries
        product_dicts = []
        for product in products:
            product_dicts.append({
                "id": product.id,
                "description": product.description,
                "hsn_code": product.hsn_code,
                "available_quantity": product.available_quantity,
                "uom": product.uom,
                "rate": float(product.rate) if product.rate is not None else 0.0  # Handle None values
            })

        return product_dicts
    except Exception as e:
        print(f"Error fetching products: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch products: {str(e)}"
        )

@app.get("/products/search/")
def search_products(query: str, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    products = db.query(Product).filter(
        Product.user_id == current_user.id,
        Product.description.ilike(f"%{query}%")
    ).all()
    return products

# Update a product
@app.put("/products/{product_id}", response_model=ProductResponse)
def update_product(
    product_id: int,
    product: ProductCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    try:
        db_product = db.query(Product).filter(
            Product.id == product_id,
            Product.user_id == current_user.id
        ).first()

        if not db_product:
            raise HTTPException(status_code=404, detail="Product not found")

        # Validate available_quantity is a valid integer
        try:
            available_quantity = int(product.available_quantity)
            if available_quantity < 0:
                raise ValueError("Available quantity cannot be negative")
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid available quantity: {str(e)}"
            )

        # Update product fields
        db_product.description = product.description
        db_product.hsn_code = product.hsn_code
        db_product.available_quantity = available_quantity
        db_product.uom = product.uom
        db_product.rate = product.rate

        # Log the update for debugging
        print(f"Updating product {product_id} with quantity: {available_quantity}")

        db.commit()
        db.refresh(db_product)
        return db_product

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        db.rollback()
        print(f"Error updating product: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update product: {str(e)}"
        )

# Delete a product
@app.delete("/products/{product_id}", status_code=204)
def delete_product(
    product_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    db_product = db.query(Product).filter(
        Product.id == product_id,
        Product.user_id == current_user.id
    ).first()

    if not db_product:
        raise HTTPException(status_code=404, detail="Product not found")

    db.delete(db_product)
    db.commit()
    return None

# Check product availability
@app.get("/products/{product_id}/availability")
def check_product_availability(
    product_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    product = db.query(Product).filter(
        Product.id == product_id,
        Product.user_id == current_user.id
    ).first()

    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    return {
        "product_id": product.id,
        "description": product.description,
        "available_quantity": product.available_quantity
    }


# Invoice endpoints
@app.post("/invoices/")
def create_invoice(invoice: InvoiceCreate, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    # Check if profile is complete enough for invoice creation
    if not current_user.is_profile_complete_for_invoice():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Please complete your profile before creating invoices. You need to fill in Personal Info and Company Details sections."
        )

    # Check if user can create more invoices based on their subscription
    if not current_user.can_create_invoice():
        remaining = max(0, 15 - current_user.invoice_count) if current_user.subscription_plan == "free" else 0
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Invoice limit reached. You have {remaining} invoices remaining on your free plan. Please upgrade to create unlimited invoices."
        )

    # Import SubscriptionService for incrementing invoice count
    from subscription_service import SubscriptionService

    # Get last invoice number
    last_invoice = db.query(Invoice).filter(Invoice.user_id == current_user.id).order_by(Invoice.id.desc()).first()
    last_invoice_number = last_invoice.invoice_number if last_invoice else None
    invoice_number = generate_invoice_number(current_user, last_invoice_number)

    # Calculate invoice items and totals
    items = []
    taxable_value = 0.0

    # First check if all products have sufficient quantity
    for item in invoice.items:
        # Find the product by description
        product = db.query(Product).filter(
            Product.user_id == current_user.id,
            Product.description == item.description
        ).first()

        if not product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Product '{item.description}' not found"
            )

        if product.available_quantity < item.quantity:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Insufficient quantity for '{item.description}'. Available: {product.available_quantity}, Requested: {item.quantity}"
            )

    # Process items and update inventory
    for item in invoice.items:
        item_taxable = calculate_taxable_value(item.quantity, item.rate, item.discount)
        taxable_value += item_taxable

        # Find the product again to update its quantity
        product = db.query(Product).filter(
            Product.user_id == current_user.id,
            Product.description == item.description
        ).first()

        # Deduct the quantity from available_quantity
        product.available_quantity -= item.quantity

        db_item = InvoiceItem(
            sr_no=item.sr_no,
            description=item.description,
            hsn_code=item.hsn_code,
            quantity=item.quantity,
            uom=item.uom,
            rate=item.rate,
            discount=item.discount,
            sgst_rate=item.sgst_rate,
            cgst_rate=item.cgst_rate,
            igst_rate=item.igst_rate,
            taxable_value=item_taxable
        )
        items.append(db_item)

    # Calculate total taxable amount (taxable value + freight & forwarding)
    total_taxable_amount = taxable_value + invoice.freight_forwarding

    # Calculate weighted average GST rates based on taxable value
    total_sgst_rate = 0
    total_cgst_rate = 0
    total_igst_rate = 0

    if taxable_value > 0:
        # Calculate weighted average GST rates
        for item in invoice.items:
            item_taxable = calculate_taxable_value(item.quantity, item.rate, item.discount)
            item_weight = item_taxable / taxable_value
            total_sgst_rate += item_weight * item.sgst_rate
            total_cgst_rate += item_weight * item.cgst_rate
            total_igst_rate += item_weight * item.igst_rate
    elif invoice.items:
        # If taxable_value is 0 but we have items, use the rates from the first item
        total_sgst_rate = invoice.items[0].sgst_rate
        total_cgst_rate = invoice.items[0].cgst_rate
        total_igst_rate = invoice.items[0].igst_rate

    # Calculate GST based on total taxable amount
    sgst = (total_taxable_amount * total_sgst_rate) / 100
    cgst = (total_taxable_amount * total_cgst_rate) / 100
    igst = (total_taxable_amount * total_igst_rate) / 100

    # Calculate grand total using the total taxable amount
    grand_total = total_taxable_amount + sgst + cgst + igst
    amount_words = amount_to_words(grand_total)

    db_invoice = Invoice(
        invoice_number=invoice_number,
        invoice_date=invoice.invoice_date if invoice.invoice_date else datetime.now(UTC),
        po_number=invoice.po_number,
        po_date=invoice.po_date,
        reverse_charge=invoice.reverse_charge,
        vehicle_number=invoice.vehicle_number,
        transporter_name=invoice.transporter_name,
        taxable_value=taxable_value,
        freight_forwarding=invoice.freight_forwarding,
        sgst=sgst,
        cgst=cgst,
        igst=igst,
        grand_total=grand_total,
        amount_in_words=amount_words,
        user_id=current_user.id,
        customer_id=invoice.customer_id,
        # Consignee (Ship To) details
        consignee_name=invoice.consignee_name,
        consignee_address=invoice.consignee_address,
        consignee_state=invoice.consignee_state,
        consignee_state_code=invoice.consignee_state_code,
        consignee_gstin=invoice.consignee_gstin
    )

    db.add(db_invoice)
    db.commit()

    # Add items to invoice
    for item in items:
        item.invoice_id = db_invoice.id
        db.add(item)

    db.commit()
    db.refresh(db_invoice)

    # Increment user's invoice count for free plan tracking
    if current_user.subscription_plan == "free":
        current_user.invoice_count += 1
        current_user.updated_at = datetime.now()
        db.commit()

    return db_invoice

# Read invoices
@app.get("/invoices/")
def read_invoices(skip: int = 0, limit: int = 1000, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    try:
        # Increased default limit to 1000 to ensure all invoices are returned
        # Count total invoices for debugging
        total_invoices = db.query(Invoice).filter(Invoice.user_id == current_user.id).count()
        print(f"Total invoices in database: {total_invoices}")

        # Get all invoice IDs first to see what's available
        all_invoice_ids = [inv.id for inv in db.query(Invoice.id).filter(Invoice.user_id == current_user.id).all()]
        print(f"All invoice IDs: {all_invoice_ids}")

        # Try a simpler query first without joins to see if we get all invoices
        # Sort by ID in descending order to get the most recently created invoices first
        simple_invoices = db.query(Invoice).filter(
            Invoice.user_id == current_user.id
        ).order_by(
            Invoice.id.desc()  # Sort by ID desc to get the most recently created
        ).all()
        print(f"Simple query returns {len(simple_invoices)} invoices")

        # Use the simple query result instead of the join query
        invoices = simple_invoices

        # For each invoice, manually load the customer if it exists
        for invoice in invoices:
            if invoice.customer_id:
                customer = db.query(Customer).filter(Customer.id == invoice.customer_id).first()
                if customer:
                    invoice.customer = customer
                else:
                    invoice.customer = None
            else:
                invoice.customer = None

        # Get IDs of returned invoices for comparison
        returned_ids = [inv.id for inv in invoices]
        print(f"Returned invoice IDs: {returned_ids}")

        # Add print statement for debugging
        print(f"Returning {len(invoices)} invoices")
        if invoices:
            print("First invoice customer:", invoices[0].customer if invoices[0].customer else "No customer for this invoice")

        # Prepare the response data with proper error handling
        result = []
        for invoice in invoices:
            try:
                invoice_data = {
                    "id": invoice.id,
                    "invoice_number": invoice.invoice_number,
                    "invoice_date": invoice.invoice_date,
                    "taxable_value": float(invoice.taxable_value) if invoice.taxable_value is not None else 0.0,
                    "grand_total": float(invoice.grand_total) if invoice.grand_total is not None else 0.0,
                    "customer": {
                        "id": invoice.customer.id,
                        "name": invoice.customer.name
                    } if invoice.customer else {
                        "id": None,
                        "name": "Unknown Customer"
                    }
                }
                result.append(invoice_data)
            except Exception as item_error:
                print(f"Error processing invoice {invoice.id}: {str(item_error)}")
                # Continue with next invoice instead of failing completely

        print(f"Successfully processed {len(result)} invoices")
        return result
    except Exception as e:
        print(f"Error fetching invoices: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching invoices: {str(e)}")

# Get a single invoice with all details
@app.get("/invoices/{invoice_id}")
def get_invoice(invoice_id: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    invoice = db.query(Invoice).filter(Invoice.id == invoice_id, Invoice.user_id == current_user.id).first()
    if not invoice:
        raise HTTPException(status_code=404, detail="Invoice not found")

    customer = db.query(Customer).filter(Customer.id == invoice.customer_id).first()
    items = db.query(InvoiceItem).filter(InvoiceItem.invoice_id == invoice_id).all()

    # Format items for response
    formatted_items = []
    for item in items:
        formatted_items.append({
            "sr_no": item.sr_no,
            "description": item.description,
            "hsn_code": item.hsn_code,
            "quantity": item.quantity,
            "uom": item.uom,
            "rate": item.rate,
            "discount": getattr(item, 'discount', 0),
            "sgst_rate": getattr(item, 'sgst_rate', 0),
            "cgst_rate": getattr(item, 'cgst_rate', 0),
            "igst_rate": getattr(item, 'igst_rate', 0),
            "taxable_value": item.taxable_value
        })

    return {
        "id": invoice.id,
        "invoice_number": invoice.invoice_number,
        "invoice_date": invoice.invoice_date,
        "po_number": invoice.po_number,
        "po_date": invoice.po_date,
        "reverse_charge": invoice.reverse_charge,
        "vehicle_number": invoice.vehicle_number,
        "transporter_name": invoice.transporter_name,
        "taxable_value": invoice.taxable_value,
        "freight_forwarding": invoice.freight_forwarding,
        "sgst": invoice.sgst,
        "cgst": invoice.cgst,
        "igst": invoice.igst,
        "grand_total": invoice.grand_total,
        "amount_in_words": invoice.amount_in_words,
        "customer": {
            "id": customer.id,
            "name": customer.name,
            "address": customer.address,
            "state": customer.state,
            "state_code": customer.state_code,
            "gstin": customer.gstin,
            "mobile_number": customer.mobile_number
        },
        # Consignee (Ship To) details
        "consignee_name": invoice.consignee_name or (customer.name if customer else None),
        "consignee_address": invoice.consignee_address or (customer.address if customer else None),
        "consignee_state": invoice.consignee_state or (customer.state if customer else None),
        "consignee_state_code": invoice.consignee_state_code or (customer.state_code if customer else None),
        "consignee_gstin": invoice.consignee_gstin or (customer.gstin if customer else None),
        "items": formatted_items
    }

# Invoice PDF generation
@app.get("/invoices/{invoice_id}/pdf")
def get_invoice_pdf(
    invoice_id: int,
    template_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    try:
        invoice = db.query(Invoice).filter(Invoice.id == invoice_id, Invoice.user_id == current_user.id).first()
        if not invoice:
            raise HTTPException(status_code=404, detail="Invoice not found")

        # Get customer if it exists
        customer = None
        if invoice.customer_id:
            customer = db.query(Customer).filter(Customer.id == invoice.customer_id).first()

        # Log for debugging
        print(f"Invoice ID: {invoice_id}, Customer ID: {invoice.customer_id}, Customer: {customer}")

        items = db.query(InvoiceItem).filter(InvoiceItem.invoice_id == invoice_id).all()

        # Get template configuration if template_id is provided
        template_config = None
        if template_id:
            from models import InvoiceTemplate
            import json

            template = db.query(InvoiceTemplate).filter(
                InvoiceTemplate.id == template_id,
                InvoiceTemplate.user_id == current_user.id
            ).first()

            if template:
                template_config = {
                    "colors": json.loads(template.colors) if template.colors else {},
                    "fonts": json.loads(template.fonts) if template.fonts else {},
                    "layout": json.loads(template.layout) if template.layout else {},
                    "fields": json.loads(template.fields) if template.fields else {},
                    "branding": json.loads(template.branding) if template.branding else {}
                }
                print(f"Using template: {template.name} (ID: {template_id})")
            else:
                print(f"Template {template_id} not found, using default template")

        pdf_buffer = generate_invoice_pdf(invoice, current_user, customer, items, template_config)

        # Format the filename as invoice_number_customer_name_current_date.pdf
        current_date = datetime.now().strftime("%Y-%m-%d")
        customer_name = "Unknown"
        if customer:
            try:
                customer_name = customer.name.replace(" ", "_")
            except:
                customer_name = "Unknown"
        filename = f"{invoice.invoice_number}_{customer_name}_{current_date}.pdf"
    except Exception as e:
        print(f"Error generating invoice PDF: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error generating invoice PDF: {str(e)}")

    return StreamingResponse(
        pdf_buffer,
        media_type="application/pdf",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

# Preview invoice PDF (same as download but with different Content-Disposition)
@app.get("/invoices/{invoice_id}/preview")
def preview_invoice_pdf(
    invoice_id: int,
    template_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    try:
        invoice = db.query(Invoice).filter(Invoice.id == invoice_id, Invoice.user_id == current_user.id).first()
        if not invoice:
            raise HTTPException(status_code=404, detail="Invoice not found")

        # Get customer if it exists
        customer = None
        if invoice.customer_id:
            customer = db.query(Customer).filter(Customer.id == invoice.customer_id).first()

        # Log for debugging
        print(f"Preview - Invoice ID: {invoice_id}, Customer ID: {invoice.customer_id}, Customer: {customer}")

        items = db.query(InvoiceItem).filter(InvoiceItem.invoice_id == invoice_id).all()

        # Get template configuration if template_id is provided
        template_config = None
        if template_id:
            from models import InvoiceTemplate
            import json

            template = db.query(InvoiceTemplate).filter(
                InvoiceTemplate.id == template_id,
                InvoiceTemplate.user_id == current_user.id
            ).first()

            if template:
                template_config = {
                    "colors": json.loads(template.colors) if template.colors else {},
                    "fonts": json.loads(template.fonts) if template.fonts else {},
                    "layout": json.loads(template.layout) if template.layout else {},
                    "fields": json.loads(template.fields) if template.fields else {},
                    "branding": json.loads(template.branding) if template.branding else {}
                }
                print(f"Using template: {template.name} (ID: {template_id})")
            else:
                print(f"Template {template_id} not found, using default template")

        pdf_buffer = generate_invoice_pdf(invoice, current_user, customer, items, template_config)
    except Exception as e:
        print(f"Error generating invoice preview PDF: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error generating invoice preview PDF: {str(e)}")

    # Make sure to set the correct Content-Type and Content-Disposition headers
    return StreamingResponse(
        pdf_buffer,
        media_type="application/pdf",
        headers={
            "Content-Disposition": "inline; filename=preview.pdf",
            "Content-Type": "application/pdf"
        }
    )

# Update an invoice
@app.put("/invoices/{invoice_id}")
def update_invoice(invoice_id: int, invoice_update: InvoiceCreate, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    # Check if invoice exists and belongs to current user
    db_invoice = db.query(Invoice).filter(Invoice.id == invoice_id, Invoice.user_id == current_user.id).first()
    if not db_invoice:
        raise HTTPException(status_code=404, detail="Invoice not found")

    # Get existing items to restore product quantities
    existing_items = db.query(InvoiceItem).filter(InvoiceItem.invoice_id == invoice_id).all()

    # Create a map of original quantities by product description
    original_quantities = {}
    for item in existing_items:
        original_quantities[item.description] = original_quantities.get(item.description, 0) + item.quantity

    # Restore product quantities from existing items
    for item in existing_items:
        product = db.query(Product).filter(
            Product.user_id == current_user.id,
            Product.description == item.description
        ).first()
        if product:
            product.available_quantity += item.quantity
            print(f"Restored {item.quantity} units to product '{item.description}'. New available quantity: {product.available_quantity}")

    # Delete existing items
    db.query(InvoiceItem).filter(InvoiceItem.invoice_id == invoice_id).delete()

    # Calculate new invoice items and totals
    items = []
    taxable_value = 0.0

    # Create a map of new quantities by product description
    new_quantities = {}
    for item in invoice_update.items:
        new_quantities[item.description] = new_quantities.get(item.description, 0) + item.quantity

    # First check if all products have sufficient quantity
    for item in invoice_update.items:
        # Find the product by description
        product = db.query(Product).filter(
            Product.user_id == current_user.id,
            Product.description == item.description
        ).first()

        if not product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Product '{item.description}' not found"
            )

        # Get the total quantity of this product in the updated invoice
        total_new_quantity = new_quantities[item.description]

        # Get the original quantity of this product in the invoice before update
        original_quantity = original_quantities.get(item.description, 0)

        # Calculate the net change in quantity
        net_quantity_change = total_new_quantity - original_quantity

        # Only check availability if we're increasing the quantity
        if net_quantity_change > 0 and product.available_quantity < net_quantity_change:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Insufficient quantity for '{item.description}'. Available: {product.available_quantity}, Requested additional: {net_quantity_change}"
            )

        print(f"Product: {item.description}, Original: {original_quantity}, New: {total_new_quantity}, Net Change: {net_quantity_change}, Available: {product.available_quantity}")

    # Process items and update inventory
    for item in invoice_update.items:
        item_taxable = calculate_taxable_value(item.quantity, item.rate, item.discount)
        taxable_value += item_taxable

        # Find the product again to update its quantity
        product = db.query(Product).filter(
            Product.user_id == current_user.id,
            Product.description == item.description
        ).first()

        # Deduct the quantity from available_quantity
        # We've already restored the original quantities, so we can just deduct the new quantity
        product.available_quantity -= item.quantity
        print(f"Deducted {item.quantity} units from product '{item.description}'. New available quantity: {product.available_quantity}")

        db_item = InvoiceItem(
            invoice_id=invoice_id,
            sr_no=item.sr_no,
            description=item.description,
            hsn_code=item.hsn_code,
            quantity=item.quantity,
            uom=item.uom,
            rate=item.rate,
            discount=item.discount,
            sgst_rate=item.sgst_rate,
            cgst_rate=item.cgst_rate,
            igst_rate=item.igst_rate,
            taxable_value=item_taxable
        )
        items.append(db_item)

    # Calculate new totals
    # Calculate total taxable amount (taxable value + freight & forwarding)
    total_taxable_amount = taxable_value + invoice_update.freight_forwarding

    # Calculate weighted average GST rates based on taxable value
    total_sgst_rate = 0
    total_cgst_rate = 0
    total_igst_rate = 0

    if taxable_value > 0:
        # Calculate weighted average GST rates
        for item in invoice_update.items:
            item_taxable = calculate_taxable_value(item.quantity, item.rate, item.discount)
            item_weight = item_taxable / taxable_value
            total_sgst_rate += item_weight * item.sgst_rate
            total_cgst_rate += item_weight * item.cgst_rate
            total_igst_rate += item_weight * item.igst_rate
    elif invoice_update.items:
        # If taxable_value is 0 but we have items, use the rates from the first item
        total_sgst_rate = invoice_update.items[0].sgst_rate
        total_cgst_rate = invoice_update.items[0].cgst_rate
        total_igst_rate = invoice_update.items[0].igst_rate

    # Calculate GST based on total taxable amount
    sgst = (total_taxable_amount * total_sgst_rate) / 100
    cgst = (total_taxable_amount * total_cgst_rate) / 100
    igst = (total_taxable_amount * total_igst_rate) / 100

    # Calculate grand total using the total taxable amount
    grand_total = total_taxable_amount + sgst + cgst + igst
    amount_words = amount_to_words(grand_total)

    # Update invoice fields
    db_invoice.invoice_date = invoice_update.invoice_date if invoice_update.invoice_date else db_invoice.invoice_date
    db_invoice.po_number = invoice_update.po_number
    db_invoice.po_date = invoice_update.po_date
    db_invoice.reverse_charge = invoice_update.reverse_charge
    db_invoice.vehicle_number = invoice_update.vehicle_number
    db_invoice.transporter_name = invoice_update.transporter_name
    db_invoice.taxable_value = taxable_value
    db_invoice.freight_forwarding = invoice_update.freight_forwarding
    db_invoice.sgst = sgst
    db_invoice.cgst = cgst
    db_invoice.igst = igst
    db_invoice.grand_total = grand_total
    db_invoice.amount_in_words = amount_words
    db_invoice.customer_id = invoice_update.customer_id
    # Update Consignee (Ship To) details
    db_invoice.consignee_name = invoice_update.consignee_name
    db_invoice.consignee_address = invoice_update.consignee_address
    db_invoice.consignee_state = invoice_update.consignee_state
    db_invoice.consignee_state_code = invoice_update.consignee_state_code
    db_invoice.consignee_gstin = invoice_update.consignee_gstin

    # Add new items to invoice
    for item in items:
        db.add(item)

    db.commit()
    db.refresh(db_invoice)

    return db_invoice

# Delete an invoice
@app.delete("/invoices/{invoice_id}", status_code=204)
def delete_invoice(invoice_id: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    # Check if invoice exists and belongs to current user
    db_invoice = db.query(Invoice).filter(Invoice.id == invoice_id, Invoice.user_id == current_user.id).first()
    if not db_invoice:
        raise HTTPException(status_code=404, detail="Invoice not found")

    # Get items to restore product quantities
    items = db.query(InvoiceItem).filter(InvoiceItem.invoice_id == invoice_id).all()

    # Restore product quantities
    for item in items:
        product = db.query(Product).filter(
            Product.user_id == current_user.id,
            Product.description == item.description
        ).first()
        if product:
            product.available_quantity += item.quantity

    # Delete invoice items first (due to foreign key constraint)
    db.query(InvoiceItem).filter(InvoiceItem.invoice_id == invoice_id).delete()

    # Delete the invoice
    db.delete(db_invoice)
    db.commit()

    return None

# Report endpoints
@app.post("/reports/sales")
def get_sales_report(report: ReportRequest, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    df = generate_sales_report(db, current_user.id, report.start_date, report.end_date)
    return df.to_dict(orient="records")

@app.post("/reports/tax-summary")
def get_tax_summary(report: ReportRequest, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    return generate_tax_summary(db, current_user.id, report.start_date, report.end_date)

@app.post("/reports/export-excel")
def export_excel_report(report: ReportRequest, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    # Format dates for filename
    start_date_str = report.start_date.strftime("%Y-%m-%d")
    end_date_str = report.end_date.strftime("%Y-%m-%d")
    filename = f"Sales_Report_{start_date_str}_to_{end_date_str}.xlsx"

    # Generate Excel file
    excel_data = generate_excel_report(db, current_user.id, report.start_date, report.end_date)

    # Return the Excel file as a downloadable attachment
    return StreamingResponse(
        excel_data,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

@app.post("/reports/hsn-summary")
def get_hsn_summary(report: ReportRequest, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Get HSN/SAC summary for a specific date range"""
    # Check if date range spans multiple months
    start_month = report.start_date.month
    start_year = report.start_date.year
    end_month = report.end_date.month
    end_year = report.end_date.year

    spans_multiple_months = (start_year != end_year) or (start_month != end_month)

    if spans_multiple_months:
        # Return monthly summary for multiple months
        return generate_monthly_hsn_summary(db, current_user.id, report.start_date, report.end_date)
    else:
        # Return detailed summary for a single month
        return generate_hsn_summary(db, current_user.id, report.start_date, report.end_date)

@app.post("/reports/hsn-summary/{year}/{month}")
def get_hsn_summary_for_month(year: int, month: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Get detailed HSN/SAC summary for a specific month"""
    return generate_hsn_summary_for_month(db, current_user.id, year, month)

@app.post("/reports/export-hsn-excel")
def export_hsn_excel_report(report: ReportRequest, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Export HSN/SAC summary to Excel"""
    # Format dates for filename
    start_date_str = report.start_date.strftime("%Y-%m-%d")
    end_date_str = report.end_date.strftime("%Y-%m-%d")
    filename = f"HSN_Summary_{start_date_str}_to_{end_date_str}.xlsx"

    # Generate Excel file
    excel_data = export_hsn_summary_excel(db, current_user.id, report.start_date, report.end_date)

    # Return the Excel file as a downloadable attachment
    return StreamingResponse(
        excel_data,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

# Note: Routers already included above - removing duplicates to prevent conflicts

# Add endpoint to get invoice count
@app.get("/invoices/count")
def get_invoice_count(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Get the total count of invoices for the current user"""
    try:
        count = db.query(Invoice).filter(Invoice.user_id == current_user.id).count()
        return {"total": count}
    except Exception as e:
        print(f"Error getting invoice count: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting invoice count: {str(e)}")

# Add endpoint to get latest invoice number
@app.get("/invoices/latest-number/")
def get_latest_invoice_number(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Get the latest invoice number for the current user"""
    try:
        # Get the latest invoice
        latest_invoice = db.query(Invoice).filter(Invoice.user_id == current_user.id).order_by(Invoice.id.desc()).first()

        # Return the latest invoice number or None
        return {
            "latest_number": latest_invoice.invoice_number.split('/')[-1] if latest_invoice else None,
            "full_number": latest_invoice.invoice_number if latest_invoice else None
        }
    except Exception as e:
        print(f"Error getting latest invoice number: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting latest invoice number: {str(e)}")

# Add endpoint for recent invoices

@app.get("/invoices/recent", response_model=List[Dict[str, Any]])
def get_recent_invoices(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get the 3 most recent invoices with complete customer and item details.
    Returns empty list if no invoices or on error.
    """
    try:
        # Fetch invoices with eager loading (reduces database queries)
        # Sort by ID in descending order to get the most recently created invoices
        # This ensures we get the latest invoices regardless of the invoice_date
        recent_invoices = (
            db.query(Invoice)
            .filter(Invoice.user_id == current_user.id)
            .order_by(Invoice.id.desc())  # Sort by ID desc to get the most recently created
            .limit(3)
            .options(
                joinedload(Invoice.customer),  # Eager load customer
                joinedload(Invoice.items)      # Eager load items
            )
            .all()
        )

        if not recent_invoices:
            return []

        # Build response with all required fields
        result = []
        for invoice in recent_invoices:
            invoice_data = {
                "id": invoice.id,
                "invoice_number": invoice.invoice_number,
                "invoice_date": invoice.invoice_date.isoformat(),
                "taxable_value": invoice.taxable_value,
                "cgst": invoice.cgst,
                "sgst": invoice.sgst,
                "grand_total": invoice.grand_total,
                "customer": {
                    "id": invoice.customer.id,
                    "name": invoice.customer.name,
                    "gstin": invoice.customer.gstin,
                    "address": invoice.customer.address
                } if invoice.customer else None,
                "items": [
                    {
                        "id": item.id,
                        "description": item.description,
                        "quantity": item.quantity,
                        "uom": item.uom,
                        "rate": item.rate,
                        "taxable_value": item.quantity * item.rate
                    }
                    for item in invoice.items
                ]
            }
            result.append(invoice_data)

        return result

    except Exception as e:
        traceback.print_exc()
        # Return empty array on error (as per requirements)
        return []
if __name__ == "__main__":
    import uvicorn
    import logging
    import sys

    # Configure logging to suppress KeyboardInterrupt traceback
    logging.getLogger("uvicorn").setLevel(logging.WARNING)

    try:
        # Use uvicorn's Server class directly for better control
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
    except KeyboardInterrupt:
        # Handle Ctrl+C gracefully
        print("\nShutting down server...")
        sys.exit(0)
