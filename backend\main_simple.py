from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from pydantic import BaseModel, field_validator, Field

from models import User, Base
from database import engine, get_db
from auth import get_current_user, create_access_token, authenticate_user, get_password_hash, ACCESS_TOKEN_EXPIRE_MINUTES

# Create tables
Base.metadata.create_all(bind=engine)

app = FastAPI()

# CORS configuration
origins = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "*"
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class UserCreate(BaseModel):
    email: str = Field(..., description="Email address (used as username)")
    password: str = Field(..., min_length=6, description="Password must be at least 6 characters")
    confirmPassword: str = Field(..., description="Confirm password must match password")
    company_name: str = Field(..., min_length=1, description="Company name is required")
    mobile_number: str = Field(..., description="Mobile number must be 10 digits")

    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError("Invalid email format")
        return v.lower()

    @field_validator('mobile_number')
    @classmethod
    def validate_mobile_number(cls, v):
        if not v.isdigit() or len(v) != 10:
            raise ValueError("Mobile number must be 10 digits")
        return v

    @field_validator('confirmPassword')
    @classmethod
    def passwords_match(cls, v, values):
        if 'password' in values.data and v != values.data['password']:
            raise ValueError('Passwords do not match')
        return v

class UserResponse(BaseModel):
    username: str
    email: str
    company_name: Optional[str] = None
    mobile_number: Optional[str] = None
    address: Optional[str] = None
    state: Optional[str] = None
    state_code: Optional[str] = None
    gstin: Optional[str] = None
    bank_name: Optional[str] = None
    account_number: Optional[str] = None
    branch: Optional[str] = None
    ifsc_code: Optional[str] = None
    profile_completed: Optional[bool] = None
    profile_completion_percentage: Optional[int] = None

class UserInfo(BaseModel):
    username: str
    company_name: str
    email: str

class Token(BaseModel):
    access_token: str
    token_type: str
    user_info: Optional[UserInfo] = None

# Root endpoint
@app.get("/")
def read_root():
    return {
        "message": "GST Billing Software API",
        "status": "running",
        "version": "1.0.0"
    }

# Auth endpoints
@app.post("/register", response_model=UserResponse)
def register(user: UserCreate, db: Session = Depends(get_db)):
    try:
        print(f"Registration attempt for email: {user.email}")
        
        # Check for duplicate email
        db_email = db.query(User).filter(User.email == user.email).first()
        if db_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        print("Creating user...")
        hashed_password = get_password_hash(user.password)
        db_user = User(
            username=user.email,
            email=user.email,
            hashed_password=hashed_password,
            company_name=user.company_name,
            mobile_number=user.mobile_number,
            profile_completed=False,
            profile_completion_percentage=0
        )

        print("Adding user to database...")
        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        print("Calculating profile completion...")
        completion_percentage = db_user.calculate_profile_completion()
        db_user.profile_completion_percentage = completion_percentage
        db_user.profile_completed = db_user.is_profile_complete_for_invoice()
        db.commit()
        db.refresh(db_user)

        print("Registration successful!")
        return {
            "username": db_user.username,
            "email": db_user.email,
            "company_name": db_user.company_name,
            "mobile_number": db_user.mobile_number,
            "address": db_user.address,
            "state": db_user.state,
            "state_code": db_user.state_code,
            "gstin": db_user.gstin,
            "bank_name": db_user.bank_name,
            "account_number": db_user.account_number,
            "branch": db_user.branch,
            "ifsc_code": db_user.ifsc_code,
            "profile_completed": db_user.profile_completed,
            "profile_completion_percentage": db_user.profile_completion_percentage
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"Registration error: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )

@app.post("/token", response_model=Token)
def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = authenticate_user(db, form_data.username, form_data.password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    token_data = {
        "sub": user.username,
        "company_name": user.company_name,
        "email": user.email,
        "user_id": user.id
    }

    access_token = create_access_token(
        data=token_data, expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_info": {
            "username": user.username,
            "company_name": user.company_name,
            "email": user.email
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
