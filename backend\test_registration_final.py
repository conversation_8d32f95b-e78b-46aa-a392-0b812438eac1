#!/usr/bin/env python3

import requests
import json
import sys
import time

def test_registration():
    url = "http://localhost:8000/register"
    
    # Use a unique email to avoid conflicts
    timestamp = int(time.time())
    
    test_data = {
        "email": f"newuser{timestamp}@example.com",
        "password": "password123",
        "confirmPassword": "password123",
        "company_name": "New Test Company Ltd",
        "mobile_number": "9876543210"
    }
    
    try:
        print("🧪 Testing registration endpoint...")
        print(f"📍 URL: {url}")
        print(f"📝 Data: {json.dumps(test_data, indent=2)}")
        
        # Set headers to match frontend
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        print("\n⏳ Sending request...")
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"📄 Response Data: {json.dumps(response_data, indent=2)}")
        except Exception as json_error:
            print(f"❌ JSON parsing error: {json_error}")
            print(f"📄 Raw Response Text: {response.text}")
            
        if response.status_code == 200:
            print("\n✅ Registration successful!")
            return True
        elif response.status_code == 422:
            print(f"\n❌ Validation error (422)")
            return False
        elif response.status_code == 400:
            print(f"\n❌ Bad request (400)")
            return False
        else:
            print(f"\n❌ Registration failed with status {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("\n❌ Request timed out (30 seconds)")
        return False
    except requests.exceptions.ConnectionError:
        print("\n❌ Connection error - is the backend running on port 8000?")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting registration test...")
    success = test_registration()
    
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n💥 Test failed!")
    
    sys.exit(0 if success else 1)
